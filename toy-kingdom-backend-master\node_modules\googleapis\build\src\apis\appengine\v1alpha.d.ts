/// <reference types="node" />
import { OAuth2Client, JW<PERSON>, Compute, UserRefreshClient, BaseExternalAccountClient, GaxiosPromise, GoogleConfigurable, MethodOptions, StreamMethodOptions, GlobalOptions, GoogleAuth, BodyResponseCallback, APIRequestContext } from 'googleapis-common';
import { Readable } from 'stream';
export declare namespace appengine_v1alpha {
    export interface Options extends GlobalOptions {
        version: 'v1alpha';
    }
    interface StandardParameters {
        /**
         * Auth client or API Key for the request
         */
        auth?: string | OAuth2Client | JWT | Compute | UserRefreshClient | BaseExternalAccountClient | GoogleAuth;
        /**
         * V1 error format.
         */
        '$.xgafv'?: string;
        /**
         * OAuth access token.
         */
        access_token?: string;
        /**
         * Data format for response.
         */
        alt?: string;
        /**
         * JSONP
         */
        callback?: string;
        /**
         * Selector specifying which fields to include in a partial response.
         */
        fields?: string;
        /**
         * API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.
         */
        key?: string;
        /**
         * OAuth 2.0 token for the current user.
         */
        oauth_token?: string;
        /**
         * Returns response with indentations and line breaks.
         */
        prettyPrint?: boolean;
        /**
         * Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.
         */
        quotaUser?: string;
        /**
         * Legacy upload protocol for media (e.g. "media", "multipart").
         */
        uploadType?: string;
        /**
         * Upload protocol for media (e.g. "raw", "multipart").
         */
        upload_protocol?: string;
    }
    /**
     * App Engine Admin API
     *
     * Provisions and manages developers&#39; App Engine applications.
     *
     * @example
     * ```js
     * const {google} = require('googleapis');
     * const appengine = google.appengine('v1alpha');
     * ```
     */
    export class Appengine {
        context: APIRequestContext;
        apps: Resource$Apps;
        projects: Resource$Projects;
        constructor(options: GlobalOptions, google?: GoogleConfigurable);
    }
    /**
     * An SSL certificate that a user has been authorized to administer. A user is authorized to administer any certificate that applies to one of their authorized domains.
     */
    export interface Schema$AuthorizedCertificate {
        /**
         * The SSL certificate serving the AuthorizedCertificate resource. This must be obtained independently from a certificate authority.
         */
        certificateRawData?: Schema$CertificateRawData;
        /**
         * The user-specified display name of the certificate. This is not guaranteed to be unique. Example: My Certificate.
         */
        displayName?: string | null;
        /**
         * Aggregate count of the domain mappings with this certificate mapped. This count includes domain mappings on applications for which the user does not have VIEWER permissions.Only returned by GET or LIST requests when specifically requested by the view=FULL_CERTIFICATE option.@OutputOnly
         */
        domainMappingsCount?: number | null;
        /**
         * Topmost applicable domains of this certificate. This certificate applies to these domains and their subdomains. Example: example.com.@OutputOnly
         */
        domainNames?: string[] | null;
        /**
         * The time when this certificate expires. To update the renewal time on this certificate, upload an SSL certificate with a different expiration time using AuthorizedCertificates.UpdateAuthorizedCertificate.@OutputOnly
         */
        expireTime?: string | null;
        /**
         * Relative name of the certificate. This is a unique value autogenerated on AuthorizedCertificate resource creation. Example: 12345.@OutputOnly
         */
        id?: string | null;
        /**
         * Only applicable if this certificate is managed by App Engine. Managed certificates are tied to the lifecycle of a DomainMapping and cannot be updated or deleted via the AuthorizedCertificates API. If this certificate is manually administered by the user, this field will be empty.@OutputOnly
         */
        managedCertificate?: Schema$ManagedCertificate;
        /**
         * Full path to the AuthorizedCertificate resource in the API. Example: apps/myapp/authorizedCertificates/12345.@OutputOnly
         */
        name?: string | null;
        /**
         * The full paths to user visible Domain Mapping resources that have this certificate mapped. Example: apps/myapp/domainMappings/example.com.This may not represent the full list of mapped domain mappings if the user does not have VIEWER permissions on all of the applications that have this certificate mapped. See domain_mappings_count for a complete count.Only returned by GET or LIST requests when specifically requested by the view=FULL_CERTIFICATE option.@OutputOnly
         */
        visibleDomainMappings?: string[] | null;
    }
    /**
     * A domain that a user has been authorized to administer. To authorize use of a domain, verify ownership via Search Console (https://search.google.com/search-console/welcome).
     */
    export interface Schema$AuthorizedDomain {
        /**
         * Fully qualified domain name of the domain authorized for use. Example: example.com.
         */
        id?: string | null;
        /**
         * Full path to the AuthorizedDomain resource in the API. Example: apps/myapp/authorizedDomains/example.com.@OutputOnly
         */
        name?: string | null;
    }
    /**
     * An SSL certificate obtained from a certificate authority.
     */
    export interface Schema$CertificateRawData {
        /**
         * Unencrypted PEM encoded RSA private key. This field is set once on certificate creation and then encrypted. The key size must be 2048 bits or fewer. Must include the header and footer. Example: -----BEGIN RSA PRIVATE KEY----- -----END RSA PRIVATE KEY----- @InputOnly
         */
        privateKey?: string | null;
        /**
         * PEM encoded x.509 public key certificate. This field is set once on certificate creation. Must include the header and footer. Example: -----BEGIN CERTIFICATE----- -----END CERTIFICATE-----
         */
        publicCertificate?: string | null;
    }
    /**
     * ContainerState contains the externally-visible container state that is used to communicate the state and reasoning for that state to the CLH. This data is not persisted by CCFE, but is instead derived from CCFE's internal representation of the container state.
     */
    export interface Schema$ContainerState {
        currentReasons?: Schema$Reasons;
        /**
         * The previous and current reasons for a container state will be sent for a container event. CLHs that need to know the signal that caused the container event to trigger (edges) as opposed to just knowing the state can act upon differences in the previous and current reasons.Reasons will be provided for every system: service management, data governance, abuse, and billing.If this is a CCFE-triggered event used for reconciliation then the current reasons will be set to their *_CONTROL_PLANE_SYNC state. The previous reasons will contain the last known set of non-unknown non-control_plane_sync reasons for the state.
         */
        previousReasons?: Schema$Reasons;
        /**
         * The current state of the container. This state is the culmination of all of the opinions from external systems that CCFE knows about of the container.
         */
        state?: string | null;
    }
    /**
     * Metadata for the given google.longrunning.Operation during a google.appengine.v1.CreateVersionRequest.
     */
    export interface Schema$CreateVersionMetadataV1 {
        /**
         * The Cloud Build ID if one was created as part of the version create. @OutputOnly
         */
        cloudBuildId?: string | null;
    }
    /**
     * Metadata for the given google.longrunning.Operation during a google.appengine.v1alpha.CreateVersionRequest.
     */
    export interface Schema$CreateVersionMetadataV1Alpha {
        /**
         * The Cloud Build ID if one was created as part of the version create. @OutputOnly
         */
        cloudBuildId?: string | null;
    }
    /**
     * Metadata for the given google.longrunning.Operation during a google.appengine.v1beta.CreateVersionRequest.
     */
    export interface Schema$CreateVersionMetadataV1Beta {
        /**
         * The Cloud Build ID if one was created as part of the version create. @OutputOnly
         */
        cloudBuildId?: string | null;
    }
    /**
     * A domain serving an App Engine application.
     */
    export interface Schema$DomainMapping {
        /**
         * Relative name of the domain serving the application. Example: example.com.
         */
        id?: string | null;
        /**
         * Full path to the DomainMapping resource in the API. Example: apps/myapp/domainMapping/example.com.@OutputOnly
         */
        name?: string | null;
        /**
         * The resource records required to configure this domain mapping. These records must be added to the domain's DNS configuration in order to serve the application via this domain mapping.@OutputOnly
         */
        resourceRecords?: Schema$ResourceRecord[];
        /**
         * SSL configuration for this domain. If unconfigured, this domain will not serve with SSL.
         */
        sslSettings?: Schema$SslSettings;
    }
    /**
     * A generic empty message that you can re-use to avoid defining duplicated empty messages in your APIs. A typical example is to use it as the request or the response type of an API method. For instance: service Foo { rpc Bar(google.protobuf.Empty) returns (google.protobuf.Empty); \}
     */
    export interface Schema$Empty {
    }
    /**
     * Metadata for the given google.cloud.location.Location.
     */
    export interface Schema$GoogleAppengineV1betaLocationMetadata {
        /**
         * App Engine flexible environment is available in the given location.@OutputOnly
         */
        flexibleEnvironmentAvailable?: boolean | null;
        /**
         * Output only. Search API (https://cloud.google.com/appengine/docs/standard/python/search) is available in the given location.
         */
        searchApiAvailable?: boolean | null;
        /**
         * App Engine standard environment is available in the given location.@OutputOnly
         */
        standardEnvironmentAvailable?: boolean | null;
    }
    /**
     * Response message for AuthorizedCertificates.ListAuthorizedCertificates.
     */
    export interface Schema$ListAuthorizedCertificatesResponse {
        /**
         * The SSL certificates the user is authorized to administer.
         */
        certificates?: Schema$AuthorizedCertificate[];
        /**
         * Continuation token for fetching the next page of results.
         */
        nextPageToken?: string | null;
    }
    /**
     * Response message for AuthorizedDomains.ListAuthorizedDomains.
     */
    export interface Schema$ListAuthorizedDomainsResponse {
        /**
         * The authorized domains belonging to the user.
         */
        domains?: Schema$AuthorizedDomain[];
        /**
         * Continuation token for fetching the next page of results.
         */
        nextPageToken?: string | null;
    }
    /**
     * Response message for DomainMappings.ListDomainMappings.
     */
    export interface Schema$ListDomainMappingsResponse {
        /**
         * The domain mappings for the application.
         */
        domainMappings?: Schema$DomainMapping[];
        /**
         * Continuation token for fetching the next page of results.
         */
        nextPageToken?: string | null;
    }
    /**
     * The response message for Locations.ListLocations.
     */
    export interface Schema$ListLocationsResponse {
        /**
         * A list of locations that matches the specified filter in the request.
         */
        locations?: Schema$Location[];
        /**
         * The standard List next-page token.
         */
        nextPageToken?: string | null;
    }
    /**
     * The response message for Operations.ListOperations.
     */
    export interface Schema$ListOperationsResponse {
        /**
         * The standard List next-page token.
         */
        nextPageToken?: string | null;
        /**
         * A list of operations that matches the specified filter in the request.
         */
        operations?: Schema$Operation[];
    }
    /**
     * A resource that represents a Google Cloud location.
     */
    export interface Schema$Location {
        /**
         * The friendly name for this location, typically a nearby city name. For example, "Tokyo".
         */
        displayName?: string | null;
        /**
         * Cross-service attributes for the location. For example {"cloud.googleapis.com/region": "us-east1"\}
         */
        labels?: {
            [key: string]: string;
        } | null;
        /**
         * The canonical id for this location. For example: "us-east1".
         */
        locationId?: string | null;
        /**
         * Service-specific metadata. For example the available capacity at the given location.
         */
        metadata?: {
            [key: string]: any;
        } | null;
        /**
         * Resource name for the location, which may vary between implementations. For example: "projects/example-project/locations/us-east1"
         */
        name?: string | null;
    }
    /**
     * Metadata for the given google.cloud.location.Location.
     */
    export interface Schema$LocationMetadata {
        /**
         * App Engine flexible environment is available in the given location.@OutputOnly
         */
        flexibleEnvironmentAvailable?: boolean | null;
        /**
         * Output only. Search API (https://cloud.google.com/appengine/docs/standard/python/search) is available in the given location.
         */
        searchApiAvailable?: boolean | null;
        /**
         * App Engine standard environment is available in the given location.@OutputOnly
         */
        standardEnvironmentAvailable?: boolean | null;
    }
    /**
     * A certificate managed by App Engine.
     */
    export interface Schema$ManagedCertificate {
        /**
         * Time at which the certificate was last renewed. The renewal process is fully managed. Certificate renewal will automatically occur before the certificate expires. Renewal errors can be tracked via ManagementStatus.@OutputOnly
         */
        lastRenewalTime?: string | null;
        /**
         * Status of certificate management. Refers to the most recent certificate acquisition or renewal attempt.@OutputOnly
         */
        status?: string | null;
    }
    /**
     * This resource represents a long-running operation that is the result of a network API call.
     */
    export interface Schema$Operation {
        /**
         * If the value is false, it means the operation is still in progress. If true, the operation is completed, and either error or response is available.
         */
        done?: boolean | null;
        /**
         * The error result of the operation in case of failure or cancellation.
         */
        error?: Schema$Status;
        /**
         * Service-specific metadata associated with the operation. It typically contains progress information and common metadata such as create time. Some services might not provide such metadata. Any method that returns a long-running operation should document the metadata type, if any.
         */
        metadata?: {
            [key: string]: any;
        } | null;
        /**
         * The server-assigned name, which is only unique within the same service that originally returns it. If you use the default HTTP mapping, the name should be a resource name ending with operations/{unique_id\}.
         */
        name?: string | null;
        /**
         * The normal, successful response of the operation. If the original method returns no data on success, such as Delete, the response is google.protobuf.Empty. If the original method is standard Get/Create/Update, the response should be the resource. For other methods, the response should have the type XxxResponse, where Xxx is the original method name. For example, if the original method name is TakeSnapshot(), the inferred response type is TakeSnapshotResponse.
         */
        response?: {
            [key: string]: any;
        } | null;
    }
    /**
     * Metadata for the given google.longrunning.Operation.
     */
    export interface Schema$OperationMetadataV1 {
        createVersionMetadata?: Schema$CreateVersionMetadataV1;
        /**
         * Time that this operation completed.@OutputOnly
         */
        endTime?: string | null;
        /**
         * Ephemeral message that may change every time the operation is polled. @OutputOnly
         */
        ephemeralMessage?: string | null;
        /**
         * Time that this operation was created.@OutputOnly
         */
        insertTime?: string | null;
        /**
         * API method that initiated this operation. Example: google.appengine.v1.Versions.CreateVersion.@OutputOnly
         */
        method?: string | null;
        /**
         * Name of the resource that this operation is acting on. Example: apps/myapp/services/default.@OutputOnly
         */
        target?: string | null;
        /**
         * User who requested this operation.@OutputOnly
         */
        user?: string | null;
        /**
         * Durable messages that persist on every operation poll. @OutputOnly
         */
        warning?: string[] | null;
    }
    /**
     * Metadata for the given google.longrunning.Operation.
     */
    export interface Schema$OperationMetadataV1Alpha {
        createVersionMetadata?: Schema$CreateVersionMetadataV1Alpha;
        /**
         * Time that this operation completed.@OutputOnly
         */
        endTime?: string | null;
        /**
         * Ephemeral message that may change every time the operation is polled. @OutputOnly
         */
        ephemeralMessage?: string | null;
        /**
         * Time that this operation was created.@OutputOnly
         */
        insertTime?: string | null;
        /**
         * API method that initiated this operation. Example: google.appengine.v1alpha.Versions.CreateVersion.@OutputOnly
         */
        method?: string | null;
        /**
         * Name of the resource that this operation is acting on. Example: apps/myapp/services/default.@OutputOnly
         */
        target?: string | null;
        /**
         * User who requested this operation.@OutputOnly
         */
        user?: string | null;
        /**
         * Durable messages that persist on every operation poll. @OutputOnly
         */
        warning?: string[] | null;
    }
    /**
     * Metadata for the given google.longrunning.Operation.
     */
    export interface Schema$OperationMetadataV1Beta {
        createVersionMetadata?: Schema$CreateVersionMetadataV1Beta;
        /**
         * Time that this operation completed.@OutputOnly
         */
        endTime?: string | null;
        /**
         * Ephemeral message that may change every time the operation is polled. @OutputOnly
         */
        ephemeralMessage?: string | null;
        /**
         * Time that this operation was created.@OutputOnly
         */
        insertTime?: string | null;
        /**
         * API method that initiated this operation. Example: google.appengine.v1beta.Versions.CreateVersion.@OutputOnly
         */
        method?: string | null;
        /**
         * Name of the resource that this operation is acting on. Example: apps/myapp/services/default.@OutputOnly
         */
        target?: string | null;
        /**
         * User who requested this operation.@OutputOnly
         */
        user?: string | null;
        /**
         * Durable messages that persist on every operation poll. @OutputOnly
         */
        warning?: string[] | null;
    }
    /**
     * The request sent to CLHs during project events.
     */
    export interface Schema$ProjectEvent {
        /**
         * The unique ID for this project event. CLHs can use this value to dedup repeated calls. required
         */
        eventId?: string | null;
        /**
         * Phase indicates when in the container event propagation this event is being communicated. Events are sent before and after the per-resource events are propagated. required
         */
        phase?: string | null;
        /**
         * The projects metadata for this project. required
         */
        projectMetadata?: Schema$ProjectsMetadata;
        /**
         * The state of the organization that led to this event.
         */
        state?: Schema$ContainerState;
    }
    /**
     * ProjectsMetadata is the metadata CCFE stores about the all the relevant projects (tenant, consumer, producer).
     */
    export interface Schema$ProjectsMetadata {
        /**
         * The consumer project id.
         */
        consumerProjectId?: string | null;
        /**
         * The consumer project number.
         */
        consumerProjectNumber?: string | null;
        /**
         * The CCFE state of the consumer project. It is the same state that is communicated to the CLH during project events. Notice that this field is not set in the DB, it is only set in this proto when communicated to CLH in the side channel.
         */
        consumerProjectState?: string | null;
        /**
         * The service account authorized to operate on the consumer project. Note: CCFE only propagates P4SA with default tag to CLH.
         */
        p4ServiceAccount?: string | null;
        /**
         * The producer project id.
         */
        producerProjectId?: string | null;
        /**
         * The producer project number.
         */
        producerProjectNumber?: string | null;
        /**
         * The tenant project id.
         */
        tenantProjectId?: string | null;
        /**
         * The tenant project number.
         */
        tenantProjectNumber?: string | null;
    }
    /**
     * Containers transition between and within states based on reasons sent from various systems. CCFE will provide the CLH with reasons for the current state per system.The current systems that CCFE supports are: Service Management (Inception) Data Governance (Wipeout) Abuse (Ares) Billing (Internal Cloud Billing API) Service Activation (Service Controller)
     */
    export interface Schema$Reasons {
        abuse?: string | null;
        billing?: string | null;
        dataGovernance?: string | null;
        /**
         * Consumer Container denotes if the service is active within a project or not. This information could be used to clean up resources in case service in DISABLED_FULL i.e. Service is inactive \> 30 days.
         */
        serviceActivation?: string | null;
        serviceManagement?: string | null;
    }
    /**
     * A DNS resource record.
     */
    export interface Schema$ResourceRecord {
        /**
         * Relative name of the object affected by this record. Only applicable for CNAME records. Example: 'www'.
         */
        name?: string | null;
        /**
         * Data for this record. Values vary by record type, as defined in RFC 1035 (section 5) and RFC 1034 (section 3.6.1).
         */
        rrdata?: string | null;
        /**
         * Resource record type. Example: AAAA.
         */
        type?: string | null;
    }
    /**
     * SSL configuration for a DomainMapping resource.
     */
    export interface Schema$SslSettings {
        /**
         * ID of the AuthorizedCertificate resource configuring SSL for the application. Clearing this field will remove SSL support.By default, a managed certificate is automatically created for every domain mapping. To omit SSL support or to configure SSL manually, specify no_managed_certificate on a CREATE or UPDATE request. You must be authorized to administer the AuthorizedCertificate resource to manually map it to a DomainMapping resource. Example: 12345.
         */
        certificateId?: string | null;
        /**
         * Whether the mapped certificate is an App Engine managed certificate. Managed certificates are created by default with a domain mapping. To opt out, specify no_managed_certificate on a CREATE or UPDATE request.@OutputOnly
         */
        isManagedCertificate?: boolean | null;
    }
    /**
     * The Status type defines a logical error model that is suitable for different programming environments, including REST APIs and RPC APIs. It is used by gRPC (https://github.com/grpc). Each Status message contains three pieces of data: error code, error message, and error details.You can find out more about this error model and how to work with it in the API Design Guide (https://cloud.google.com/apis/design/errors).
     */
    export interface Schema$Status {
        /**
         * The status code, which should be an enum value of google.rpc.Code.
         */
        code?: number | null;
        /**
         * A list of messages that carry the error details. There is a common set of message types for APIs to use.
         */
        details?: Array<{
            [key: string]: any;
        }> | null;
        /**
         * A developer-facing error message, which should be in English. Any user-facing error message should be localized and sent in the google.rpc.Status.details field, or localized by the client.
         */
        message?: string | null;
    }
    export class Resource$Apps {
        context: APIRequestContext;
        authorizedCertificates: Resource$Apps$Authorizedcertificates;
        authorizedDomains: Resource$Apps$Authorizeddomains;
        domainMappings: Resource$Apps$Domainmappings;
        locations: Resource$Apps$Locations;
        operations: Resource$Apps$Operations;
        constructor(context: APIRequestContext);
    }
    export class Resource$Apps$Authorizedcertificates {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Uploads the specified SSL certificate.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        create(params: Params$Resource$Apps$Authorizedcertificates$Create, options: StreamMethodOptions): GaxiosPromise<Readable>;
        create(params?: Params$Resource$Apps$Authorizedcertificates$Create, options?: MethodOptions): GaxiosPromise<Schema$AuthorizedCertificate>;
        create(params: Params$Resource$Apps$Authorizedcertificates$Create, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        create(params: Params$Resource$Apps$Authorizedcertificates$Create, options: MethodOptions | BodyResponseCallback<Schema$AuthorizedCertificate>, callback: BodyResponseCallback<Schema$AuthorizedCertificate>): void;
        create(params: Params$Resource$Apps$Authorizedcertificates$Create, callback: BodyResponseCallback<Schema$AuthorizedCertificate>): void;
        create(callback: BodyResponseCallback<Schema$AuthorizedCertificate>): void;
        /**
         * Deletes the specified SSL certificate.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        delete(params: Params$Resource$Apps$Authorizedcertificates$Delete, options: StreamMethodOptions): GaxiosPromise<Readable>;
        delete(params?: Params$Resource$Apps$Authorizedcertificates$Delete, options?: MethodOptions): GaxiosPromise<Schema$Empty>;
        delete(params: Params$Resource$Apps$Authorizedcertificates$Delete, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        delete(params: Params$Resource$Apps$Authorizedcertificates$Delete, options: MethodOptions | BodyResponseCallback<Schema$Empty>, callback: BodyResponseCallback<Schema$Empty>): void;
        delete(params: Params$Resource$Apps$Authorizedcertificates$Delete, callback: BodyResponseCallback<Schema$Empty>): void;
        delete(callback: BodyResponseCallback<Schema$Empty>): void;
        /**
         * Gets the specified SSL certificate.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Apps$Authorizedcertificates$Get, options: StreamMethodOptions): GaxiosPromise<Readable>;
        get(params?: Params$Resource$Apps$Authorizedcertificates$Get, options?: MethodOptions): GaxiosPromise<Schema$AuthorizedCertificate>;
        get(params: Params$Resource$Apps$Authorizedcertificates$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Apps$Authorizedcertificates$Get, options: MethodOptions | BodyResponseCallback<Schema$AuthorizedCertificate>, callback: BodyResponseCallback<Schema$AuthorizedCertificate>): void;
        get(params: Params$Resource$Apps$Authorizedcertificates$Get, callback: BodyResponseCallback<Schema$AuthorizedCertificate>): void;
        get(callback: BodyResponseCallback<Schema$AuthorizedCertificate>): void;
        /**
         * Lists all SSL certificates the user is authorized to administer.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Apps$Authorizedcertificates$List, options: StreamMethodOptions): GaxiosPromise<Readable>;
        list(params?: Params$Resource$Apps$Authorizedcertificates$List, options?: MethodOptions): GaxiosPromise<Schema$ListAuthorizedCertificatesResponse>;
        list(params: Params$Resource$Apps$Authorizedcertificates$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Apps$Authorizedcertificates$List, options: MethodOptions | BodyResponseCallback<Schema$ListAuthorizedCertificatesResponse>, callback: BodyResponseCallback<Schema$ListAuthorizedCertificatesResponse>): void;
        list(params: Params$Resource$Apps$Authorizedcertificates$List, callback: BodyResponseCallback<Schema$ListAuthorizedCertificatesResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListAuthorizedCertificatesResponse>): void;
        /**
         * Updates the specified SSL certificate. To renew a certificate and maintain its existing domain mappings, update certificate_data with a new certificate. The new certificate must be applicable to the same domains as the original certificate. The certificate display_name may also be updated.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        patch(params: Params$Resource$Apps$Authorizedcertificates$Patch, options: StreamMethodOptions): GaxiosPromise<Readable>;
        patch(params?: Params$Resource$Apps$Authorizedcertificates$Patch, options?: MethodOptions): GaxiosPromise<Schema$AuthorizedCertificate>;
        patch(params: Params$Resource$Apps$Authorizedcertificates$Patch, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        patch(params: Params$Resource$Apps$Authorizedcertificates$Patch, options: MethodOptions | BodyResponseCallback<Schema$AuthorizedCertificate>, callback: BodyResponseCallback<Schema$AuthorizedCertificate>): void;
        patch(params: Params$Resource$Apps$Authorizedcertificates$Patch, callback: BodyResponseCallback<Schema$AuthorizedCertificate>): void;
        patch(callback: BodyResponseCallback<Schema$AuthorizedCertificate>): void;
    }
    export interface Params$Resource$Apps$Authorizedcertificates$Create extends StandardParameters {
        /**
         * Part of `parent`. Name of the parent Application resource. Example: apps/myapp.
         */
        appsId?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$AuthorizedCertificate;
    }
    export interface Params$Resource$Apps$Authorizedcertificates$Delete extends StandardParameters {
        /**
         * Part of `name`. Name of the resource to delete. Example: apps/myapp/authorizedCertificates/12345.
         */
        appsId?: string;
        /**
         * Part of `name`. See documentation of `appsId`.
         */
        authorizedCertificatesId?: string;
    }
    export interface Params$Resource$Apps$Authorizedcertificates$Get extends StandardParameters {
        /**
         * Part of `name`. Name of the resource requested. Example: apps/myapp/authorizedCertificates/12345.
         */
        appsId?: string;
        /**
         * Part of `name`. See documentation of `appsId`.
         */
        authorizedCertificatesId?: string;
        /**
         * Controls the set of fields returned in the GET response.
         */
        view?: string;
    }
    export interface Params$Resource$Apps$Authorizedcertificates$List extends StandardParameters {
        /**
         * Part of `parent`. Name of the parent Application resource. Example: apps/myapp.
         */
        appsId?: string;
        /**
         * Maximum results to return per page.
         */
        pageSize?: number;
        /**
         * Continuation token for fetching the next page of results.
         */
        pageToken?: string;
        /**
         * Controls the set of fields returned in the LIST response.
         */
        view?: string;
    }
    export interface Params$Resource$Apps$Authorizedcertificates$Patch extends StandardParameters {
        /**
         * Part of `name`. Name of the resource to update. Example: apps/myapp/authorizedCertificates/12345.
         */
        appsId?: string;
        /**
         * Part of `name`. See documentation of `appsId`.
         */
        authorizedCertificatesId?: string;
        /**
         * Standard field mask for the set of fields to be updated. Updates are only supported on the certificate_raw_data and display_name fields.
         */
        updateMask?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$AuthorizedCertificate;
    }
    export class Resource$Apps$Authorizeddomains {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Lists all domains the user is authorized to administer.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Apps$Authorizeddomains$List, options: StreamMethodOptions): GaxiosPromise<Readable>;
        list(params?: Params$Resource$Apps$Authorizeddomains$List, options?: MethodOptions): GaxiosPromise<Schema$ListAuthorizedDomainsResponse>;
        list(params: Params$Resource$Apps$Authorizeddomains$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Apps$Authorizeddomains$List, options: MethodOptions | BodyResponseCallback<Schema$ListAuthorizedDomainsResponse>, callback: BodyResponseCallback<Schema$ListAuthorizedDomainsResponse>): void;
        list(params: Params$Resource$Apps$Authorizeddomains$List, callback: BodyResponseCallback<Schema$ListAuthorizedDomainsResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListAuthorizedDomainsResponse>): void;
    }
    export interface Params$Resource$Apps$Authorizeddomains$List extends StandardParameters {
        /**
         * Part of `parent`. Name of the parent Application resource. Example: apps/myapp.
         */
        appsId?: string;
        /**
         * Maximum results to return per page.
         */
        pageSize?: number;
        /**
         * Continuation token for fetching the next page of results.
         */
        pageToken?: string;
    }
    export class Resource$Apps$Domainmappings {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Maps a domain to an application. A user must be authorized to administer a domain in order to map it to an application. For a list of available authorized domains, see AuthorizedDomains.ListAuthorizedDomains.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        create(params: Params$Resource$Apps$Domainmappings$Create, options: StreamMethodOptions): GaxiosPromise<Readable>;
        create(params?: Params$Resource$Apps$Domainmappings$Create, options?: MethodOptions): GaxiosPromise<Schema$Operation>;
        create(params: Params$Resource$Apps$Domainmappings$Create, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        create(params: Params$Resource$Apps$Domainmappings$Create, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        create(params: Params$Resource$Apps$Domainmappings$Create, callback: BodyResponseCallback<Schema$Operation>): void;
        create(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Deletes the specified domain mapping. A user must be authorized to administer the associated domain in order to delete a DomainMapping resource.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        delete(params: Params$Resource$Apps$Domainmappings$Delete, options: StreamMethodOptions): GaxiosPromise<Readable>;
        delete(params?: Params$Resource$Apps$Domainmappings$Delete, options?: MethodOptions): GaxiosPromise<Schema$Operation>;
        delete(params: Params$Resource$Apps$Domainmappings$Delete, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        delete(params: Params$Resource$Apps$Domainmappings$Delete, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        delete(params: Params$Resource$Apps$Domainmappings$Delete, callback: BodyResponseCallback<Schema$Operation>): void;
        delete(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Gets the specified domain mapping.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Apps$Domainmappings$Get, options: StreamMethodOptions): GaxiosPromise<Readable>;
        get(params?: Params$Resource$Apps$Domainmappings$Get, options?: MethodOptions): GaxiosPromise<Schema$DomainMapping>;
        get(params: Params$Resource$Apps$Domainmappings$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Apps$Domainmappings$Get, options: MethodOptions | BodyResponseCallback<Schema$DomainMapping>, callback: BodyResponseCallback<Schema$DomainMapping>): void;
        get(params: Params$Resource$Apps$Domainmappings$Get, callback: BodyResponseCallback<Schema$DomainMapping>): void;
        get(callback: BodyResponseCallback<Schema$DomainMapping>): void;
        /**
         * Lists the domain mappings on an application.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Apps$Domainmappings$List, options: StreamMethodOptions): GaxiosPromise<Readable>;
        list(params?: Params$Resource$Apps$Domainmappings$List, options?: MethodOptions): GaxiosPromise<Schema$ListDomainMappingsResponse>;
        list(params: Params$Resource$Apps$Domainmappings$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Apps$Domainmappings$List, options: MethodOptions | BodyResponseCallback<Schema$ListDomainMappingsResponse>, callback: BodyResponseCallback<Schema$ListDomainMappingsResponse>): void;
        list(params: Params$Resource$Apps$Domainmappings$List, callback: BodyResponseCallback<Schema$ListDomainMappingsResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListDomainMappingsResponse>): void;
        /**
         * Updates the specified domain mapping. To map an SSL certificate to a domain mapping, update certificate_id to point to an AuthorizedCertificate resource. A user must be authorized to administer the associated domain in order to update a DomainMapping resource.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        patch(params: Params$Resource$Apps$Domainmappings$Patch, options: StreamMethodOptions): GaxiosPromise<Readable>;
        patch(params?: Params$Resource$Apps$Domainmappings$Patch, options?: MethodOptions): GaxiosPromise<Schema$Operation>;
        patch(params: Params$Resource$Apps$Domainmappings$Patch, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        patch(params: Params$Resource$Apps$Domainmappings$Patch, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        patch(params: Params$Resource$Apps$Domainmappings$Patch, callback: BodyResponseCallback<Schema$Operation>): void;
        patch(callback: BodyResponseCallback<Schema$Operation>): void;
    }
    export interface Params$Resource$Apps$Domainmappings$Create extends StandardParameters {
        /**
         * Part of `parent`. Name of the parent Application resource. Example: apps/myapp.
         */
        appsId?: string;
        /**
         * Whether a managed certificate should be provided by App Engine. If true, a certificate ID must be manaually set in the DomainMapping resource to configure SSL for this domain. If false, a managed certificate will be provisioned and a certificate ID will be automatically populated.
         */
        noManagedCertificate?: boolean;
        /**
         * Whether the domain creation should override any existing mappings for this domain. By default, overrides are rejected.
         */
        overrideStrategy?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$DomainMapping;
    }
    export interface Params$Resource$Apps$Domainmappings$Delete extends StandardParameters {
        /**
         * Part of `name`. Name of the resource to delete. Example: apps/myapp/domainMappings/example.com.
         */
        appsId?: string;
        /**
         * Part of `name`. See documentation of `appsId`.
         */
        domainMappingsId?: string;
    }
    export interface Params$Resource$Apps$Domainmappings$Get extends StandardParameters {
        /**
         * Part of `name`. Name of the resource requested. Example: apps/myapp/domainMappings/example.com.
         */
        appsId?: string;
        /**
         * Part of `name`. See documentation of `appsId`.
         */
        domainMappingsId?: string;
    }
    export interface Params$Resource$Apps$Domainmappings$List extends StandardParameters {
        /**
         * Part of `parent`. Name of the parent Application resource. Example: apps/myapp.
         */
        appsId?: string;
        /**
         * Maximum results to return per page.
         */
        pageSize?: number;
        /**
         * Continuation token for fetching the next page of results.
         */
        pageToken?: string;
    }
    export interface Params$Resource$Apps$Domainmappings$Patch extends StandardParameters {
        /**
         * Part of `name`. Name of the resource to update. Example: apps/myapp/domainMappings/example.com.
         */
        appsId?: string;
        /**
         * Part of `name`. See documentation of `appsId`.
         */
        domainMappingsId?: string;
        /**
         * Whether a managed certificate should be provided by App Engine. If true, a certificate ID must be manually set in the DomainMapping resource to configure SSL for this domain. If false, a managed certificate will be provisioned and a certificate ID will be automatically populated. Only applicable if ssl_settings.certificate_id is specified in the update mask.
         */
        noManagedCertificate?: boolean;
        /**
         * Required. Standard field mask for the set of fields to be updated.
         */
        updateMask?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$DomainMapping;
    }
    export class Resource$Apps$Locations {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Gets information about a location.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Apps$Locations$Get, options: StreamMethodOptions): GaxiosPromise<Readable>;
        get(params?: Params$Resource$Apps$Locations$Get, options?: MethodOptions): GaxiosPromise<Schema$Location>;
        get(params: Params$Resource$Apps$Locations$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Apps$Locations$Get, options: MethodOptions | BodyResponseCallback<Schema$Location>, callback: BodyResponseCallback<Schema$Location>): void;
        get(params: Params$Resource$Apps$Locations$Get, callback: BodyResponseCallback<Schema$Location>): void;
        get(callback: BodyResponseCallback<Schema$Location>): void;
        /**
         * Lists information about the supported locations for this service.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Apps$Locations$List, options: StreamMethodOptions): GaxiosPromise<Readable>;
        list(params?: Params$Resource$Apps$Locations$List, options?: MethodOptions): GaxiosPromise<Schema$ListLocationsResponse>;
        list(params: Params$Resource$Apps$Locations$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Apps$Locations$List, options: MethodOptions | BodyResponseCallback<Schema$ListLocationsResponse>, callback: BodyResponseCallback<Schema$ListLocationsResponse>): void;
        list(params: Params$Resource$Apps$Locations$List, callback: BodyResponseCallback<Schema$ListLocationsResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListLocationsResponse>): void;
    }
    export interface Params$Resource$Apps$Locations$Get extends StandardParameters {
        /**
         * Part of `name`. Resource name for the location.
         */
        appsId?: string;
        /**
         * Part of `name`. See documentation of `appsId`.
         */
        locationsId?: string;
    }
    export interface Params$Resource$Apps$Locations$List extends StandardParameters {
        /**
         * Part of `name`. The resource that owns the locations collection, if applicable.
         */
        appsId?: string;
        /**
         * A filter to narrow down results to a preferred subset. The filtering language accepts strings like "displayName=tokyo", and is documented in more detail in AIP-160 (https://google.aip.dev/160).
         */
        filter?: string;
        /**
         * The maximum number of results to return. If not set, the service selects a default.
         */
        pageSize?: number;
        /**
         * A page token received from the next_page_token field in the response. Send that page token to receive the subsequent page.
         */
        pageToken?: string;
    }
    export class Resource$Apps$Operations {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Apps$Operations$Get, options: StreamMethodOptions): GaxiosPromise<Readable>;
        get(params?: Params$Resource$Apps$Operations$Get, options?: MethodOptions): GaxiosPromise<Schema$Operation>;
        get(params: Params$Resource$Apps$Operations$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Apps$Operations$Get, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        get(params: Params$Resource$Apps$Operations$Get, callback: BodyResponseCallback<Schema$Operation>): void;
        get(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns UNIMPLEMENTED.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Apps$Operations$List, options: StreamMethodOptions): GaxiosPromise<Readable>;
        list(params?: Params$Resource$Apps$Operations$List, options?: MethodOptions): GaxiosPromise<Schema$ListOperationsResponse>;
        list(params: Params$Resource$Apps$Operations$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Apps$Operations$List, options: MethodOptions | BodyResponseCallback<Schema$ListOperationsResponse>, callback: BodyResponseCallback<Schema$ListOperationsResponse>): void;
        list(params: Params$Resource$Apps$Operations$List, callback: BodyResponseCallback<Schema$ListOperationsResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListOperationsResponse>): void;
    }
    export interface Params$Resource$Apps$Operations$Get extends StandardParameters {
        /**
         * Part of `name`. The name of the operation resource.
         */
        appsId?: string;
        /**
         * Part of `name`. See documentation of `appsId`.
         */
        operationsId?: string;
    }
    export interface Params$Resource$Apps$Operations$List extends StandardParameters {
        /**
         * Part of `name`. The name of the operation's parent resource.
         */
        appsId?: string;
        /**
         * The standard list filter.
         */
        filter?: string;
        /**
         * The standard list page size.
         */
        pageSize?: number;
        /**
         * The standard list page token.
         */
        pageToken?: string;
    }
    export class Resource$Projects {
        context: APIRequestContext;
        locations: Resource$Projects$Locations;
        constructor(context: APIRequestContext);
    }
    export class Resource$Projects$Locations {
        context: APIRequestContext;
        applications: Resource$Projects$Locations$Applications;
        operations: Resource$Projects$Locations$Operations;
        constructor(context: APIRequestContext);
        /**
         * Gets information about a location.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Locations$Get, options: StreamMethodOptions): GaxiosPromise<Readable>;
        get(params?: Params$Resource$Projects$Locations$Get, options?: MethodOptions): GaxiosPromise<Schema$Location>;
        get(params: Params$Resource$Projects$Locations$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Locations$Get, options: MethodOptions | BodyResponseCallback<Schema$Location>, callback: BodyResponseCallback<Schema$Location>): void;
        get(params: Params$Resource$Projects$Locations$Get, callback: BodyResponseCallback<Schema$Location>): void;
        get(callback: BodyResponseCallback<Schema$Location>): void;
        /**
         * Lists information about the supported locations for this service.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Locations$List, options: StreamMethodOptions): GaxiosPromise<Readable>;
        list(params?: Params$Resource$Projects$Locations$List, options?: MethodOptions): GaxiosPromise<Schema$ListLocationsResponse>;
        list(params: Params$Resource$Projects$Locations$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Locations$List, options: MethodOptions | BodyResponseCallback<Schema$ListLocationsResponse>, callback: BodyResponseCallback<Schema$ListLocationsResponse>): void;
        list(params: Params$Resource$Projects$Locations$List, callback: BodyResponseCallback<Schema$ListLocationsResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListLocationsResponse>): void;
    }
    export interface Params$Resource$Projects$Locations$Get extends StandardParameters {
        /**
         * Part of `name`. See documentation of `projectsId`.
         */
        locationsId?: string;
        /**
         * Part of `name`. Resource name for the location.
         */
        projectsId?: string;
    }
    export interface Params$Resource$Projects$Locations$List extends StandardParameters {
        /**
         * A filter to narrow down results to a preferred subset. The filtering language accepts strings like "displayName=tokyo", and is documented in more detail in AIP-160 (https://google.aip.dev/160).
         */
        filter?: string;
        /**
         * The maximum number of results to return. If not set, the service selects a default.
         */
        pageSize?: number;
        /**
         * A page token received from the next_page_token field in the response. Send that page token to receive the subsequent page.
         */
        pageToken?: string;
        /**
         * Part of `name`. The resource that owns the locations collection, if applicable.
         */
        projectsId?: string;
    }
    export class Resource$Projects$Locations$Applications {
        context: APIRequestContext;
        authorizedDomains: Resource$Projects$Locations$Applications$Authorizeddomains;
        constructor(context: APIRequestContext);
    }
    export class Resource$Projects$Locations$Applications$Authorizeddomains {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Lists all domains the user is authorized to administer.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Locations$Applications$Authorizeddomains$List, options: StreamMethodOptions): GaxiosPromise<Readable>;
        list(params?: Params$Resource$Projects$Locations$Applications$Authorizeddomains$List, options?: MethodOptions): GaxiosPromise<Schema$ListAuthorizedDomainsResponse>;
        list(params: Params$Resource$Projects$Locations$Applications$Authorizeddomains$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Locations$Applications$Authorizeddomains$List, options: MethodOptions | BodyResponseCallback<Schema$ListAuthorizedDomainsResponse>, callback: BodyResponseCallback<Schema$ListAuthorizedDomainsResponse>): void;
        list(params: Params$Resource$Projects$Locations$Applications$Authorizeddomains$List, callback: BodyResponseCallback<Schema$ListAuthorizedDomainsResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListAuthorizedDomainsResponse>): void;
    }
    export interface Params$Resource$Projects$Locations$Applications$Authorizeddomains$List extends StandardParameters {
        /**
         * Part of `parent`. See documentation of `projectsId`.
         */
        applicationsId?: string;
        /**
         * Part of `parent`. See documentation of `projectsId`.
         */
        locationsId?: string;
        /**
         * Maximum results to return per page.
         */
        pageSize?: number;
        /**
         * Continuation token for fetching the next page of results.
         */
        pageToken?: string;
        /**
         * Part of `parent`. Name of the parent Application resource. Example: apps/myapp.
         */
        projectsId?: string;
    }
    export class Resource$Projects$Locations$Operations {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Locations$Operations$Get, options: StreamMethodOptions): GaxiosPromise<Readable>;
        get(params?: Params$Resource$Projects$Locations$Operations$Get, options?: MethodOptions): GaxiosPromise<Schema$Operation>;
        get(params: Params$Resource$Projects$Locations$Operations$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Locations$Operations$Get, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        get(params: Params$Resource$Projects$Locations$Operations$Get, callback: BodyResponseCallback<Schema$Operation>): void;
        get(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns UNIMPLEMENTED.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Locations$Operations$List, options: StreamMethodOptions): GaxiosPromise<Readable>;
        list(params?: Params$Resource$Projects$Locations$Operations$List, options?: MethodOptions): GaxiosPromise<Schema$ListOperationsResponse>;
        list(params: Params$Resource$Projects$Locations$Operations$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Locations$Operations$List, options: MethodOptions | BodyResponseCallback<Schema$ListOperationsResponse>, callback: BodyResponseCallback<Schema$ListOperationsResponse>): void;
        list(params: Params$Resource$Projects$Locations$Operations$List, callback: BodyResponseCallback<Schema$ListOperationsResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListOperationsResponse>): void;
    }
    export interface Params$Resource$Projects$Locations$Operations$Get extends StandardParameters {
        /**
         * Part of `name`. See documentation of `projectsId`.
         */
        locationsId?: string;
        /**
         * Part of `name`. See documentation of `projectsId`.
         */
        operationsId?: string;
        /**
         * Part of `name`. The name of the operation resource.
         */
        projectsId?: string;
    }
    export interface Params$Resource$Projects$Locations$Operations$List extends StandardParameters {
        /**
         * The standard list filter.
         */
        filter?: string;
        /**
         * Part of `name`. See documentation of `projectsId`.
         */
        locationsId?: string;
        /**
         * The standard list page size.
         */
        pageSize?: number;
        /**
         * The standard list page token.
         */
        pageToken?: string;
        /**
         * Part of `name`. The name of the operation's parent resource.
         */
        projectsId?: string;
    }
    export {};
}
